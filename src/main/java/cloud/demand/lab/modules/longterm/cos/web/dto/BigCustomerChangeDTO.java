package cloud.demand.lab.modules.longterm.cos.web.dto;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutBigCustomerChangeDO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 大客户历史变动数据DTO
 * 只包含主要业务字段，不包含软删除字段、createTime、updateTime等
 */
@Data
public class BigCustomerChangeDTO {

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 是否外部客户，0表示否，1表示是
     */
    private Boolean isOutCustomer;

    /**
     * 变动开始时间
     */
    private LocalDate startDate;

    /**
     * 变动结束时间
     */
    private LocalDate endDate;

    /**
     * 变化量(单位PB)
     */
    private BigDecimal netChange;

    /**
     * 从DO转换为DTO
     */
    public static BigCustomerChangeDTO fromDO(CosLongtermPredictOutBigCustomerChangeDO dO) {
        if (dO == null) {
            return null;
        }

        BigCustomerChangeDTO dto = new BigCustomerChangeDTO();
        dto.setCustomerName(dO.getCustomerName());
        dto.setIsOutCustomer(dO.getIsOutCustomer());
        dto.setStartDate(dO.getStartDate());
        dto.setEndDate(dO.getEndDate());
        dto.setNetChange(dO.getNetChange());
        return dto;
    }

}
