package cloud.demand.lab.modules.longterm.cos.web;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.longterm.cos.service.CosQueryPredictTaskService;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryTaskInputArgsReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryTaskInputArgsResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * 负责查询cos中长期预测任务的信息
 */
@JsonrpcController("/cos-longterm-predict")
@Slf4j
public class CosQueryPredictTaskController {

    @Resource
    private CosQueryPredictTaskService cosQueryPredictTaskService;

    @RequestMapping
    public QueryCategoryAndTaskListResp queryCategoryAndTaskList(@JsonrpcParam QueryCategoryAndTaskListReq req) {
        return cosQueryPredictTaskService.queryCategoryAndTaskList(req);
    }

    /**
     * 查询该方案最后一个enable的任务的参数
     */
    @RequestMapping
    public QueryTaskInputArgsResp queryTaskInputArgs(@JsonrpcParam QueryTaskInputArgsReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数不能为空");
        }
        if (req.getTaskId() == null) {
            throw new WrongWebParameterException("任务id不能为空");
        }
        return cosQueryPredictTaskService.queryLastInputArgs(req);
    }

}
