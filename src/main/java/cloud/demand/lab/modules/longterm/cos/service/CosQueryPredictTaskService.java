package cloud.demand.lab.modules.longterm.cos.service;

import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryTaskInputArgsReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryTaskInputArgsResp;
import org.springframework.stereotype.Service;

/**
 * 查询COS中长期预测任务信息
 */
@Service
public interface CosQueryPredictTaskService {

    /**
     * 查询已经存在的COS预测任务列表
     */
    QueryCategoryAndTaskListResp queryCategoryAndTaskList(QueryCategoryAndTaskListReq req);

    /**
     * 查询该方案最后一个enable的任务的参数
     */
    QueryTaskInputArgsResp queryLastInputArgs(QueryTaskInputArgsReq req);

}
