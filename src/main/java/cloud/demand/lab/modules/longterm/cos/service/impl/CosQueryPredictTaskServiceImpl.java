package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputArgsDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutBigCustomerChangeDO;
import cloud.demand.lab.modules.longterm.cos.web.dto.BigCustomerChangeDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.InputArgsDTO;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryTaskInputArgsReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryTaskInputArgsResp;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.lang.DateUtils;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.service.CosQueryPredictTaskService;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class CosQueryPredictTaskServiceImpl implements CosQueryPredictTaskService {

    @Resource
    private DBHelper cdLabDbHelper;

    @Override
    public QueryCategoryAndTaskListResp queryCategoryAndTaskList(QueryCategoryAndTaskListReq req) {
        // 查询所有启用的COS预测任务
        List<CosLongtermPredictTaskDO> tasks = cdLabDbHelper.getAll(CosLongtermPredictTaskDO.class, "where is_enable=1");

        // 如果还没有任何cos预测任务，那么只列cos方案
        if (ListUtils.isEmpty(tasks)) {
            QueryCategoryAndTaskListResp resp = new QueryCategoryAndTaskListResp();
            resp.setCategoryList(new ArrayList<>());
            List<CosLongtermPredictCategoryConfigDO> categories = cdLabDbHelper.getAll(CosLongtermPredictCategoryConfigDO.class);
            for (CosLongtermPredictCategoryConfigDO category : categories) {
                QueryCategoryAndTaskListResp.Category categoryResp = new QueryCategoryAndTaskListResp.Category();
                categoryResp.setCategoryId(category.getId());
                categoryResp.setCategoryName(category.getCategory());
                categoryResp.setModelPart(category.getModelPart());
                categoryResp.setPredictTaskList(new ArrayList<>());
                resp.getCategoryList().add(categoryResp);
            }
            return resp;
        }

        // 按方案ID分组
        Map<Long, List<CosLongtermPredictTaskDO>> categoryList = ListUtils.toMapList(tasks, o -> o.getCategoryId(), o -> o);

        QueryCategoryAndTaskListResp resp = new QueryCategoryAndTaskListResp();
        List<QueryCategoryAndTaskListResp.Category> categoryResult = new ArrayList<>();
        resp.setCategoryList(categoryResult);

        for (List<CosLongtermPredictTaskDO> t : categoryList.values()) {
            // 按预测开始时间倒序排列
            ListUtils.sortDescNullLast(t, CosLongtermPredictTaskDO::getPredictStart);

            QueryCategoryAndTaskListResp.Category category = new QueryCategoryAndTaskListResp.Category();
            category.setCategoryId(t.get(0).getCategoryId());
            category.setCategoryName(t.get(0).getCategoryName());
            category.setModelPart(t.get(0).getModelPart());

            List<QueryCategoryAndTaskListResp.Task> taskList = ListUtils.transform(t, o -> {
                QueryCategoryAndTaskListResp.Task task = new QueryCategoryAndTaskListResp.Task();
                task.setTaskId(o.getId());
                task.setYearMonth(DateUtils.format(o.getPredictStart(), "yyyy-MM"));
                task.setTaskStatusCode(o.getTaskStatus());
                task.setTaskStatusName(LongtermPredictTaskStatusEnum.getNameByCode(o.getTaskStatus()));
                return task;
            });
            category.setPredictTaskList(taskList);
            categoryResult.add(category);
        }

        // 按方案ID升序排列
        ListUtils.sortAscNullLast(categoryResult, QueryCategoryAndTaskListResp.Category::getCategoryId);
        return resp;
    }

    @Override
    public QueryTaskInputArgsResp queryLastInputArgs(QueryTaskInputArgsReq req) {
        QueryTaskInputArgsResp resp = new QueryTaskInputArgsResp();
        resp.setInputArgs(new ArrayList<>());

        if (req == null || req.getCategoryId() == null) {
            return resp;
        }

        // 1. 查询该方案最后一个enable的任务（按predict_start倒序）
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("category_id = ?", req.getCategoryId());
        whereSQL.and("is_enable = ?", true);
        whereSQL.addOrderBy("predict_start desc");

        CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getOne(CosLongtermPredictTaskDO.class,
                whereSQL.getSQL(), whereSQL.getParams());
        if (taskDO == null) {
            return resp;
        }

        // 2. 查询该任务的输入参数
        List<CosLongtermPredictInputArgsDO> inputArgsDOList = cdLabDbHelper.getAll(
                CosLongtermPredictInputArgsDO.class, "where task_id=?", taskDO.getId());

        if (ListUtils.isEmpty(inputArgsDOList)) {
            return resp;
        }

        // 3. 转换DO为DTO并查询大客户未来预测数据
        List<InputArgsDTO> inputArgsDTOList = new ArrayList<>();
        for (CosLongtermPredictInputArgsDO inputArgsDO : inputArgsDOList) {
            InputArgsDTO inputArgsDTO = InputArgsDTO.fromDO(inputArgsDO);

            // 查询该策略类型的大客户未来预测数据
            List<CosLongtermPredictOutBigCustomerChangeDO> bigCustomerForecastDOList =
                    cdLabDbHelper.getAll(CosLongtermPredictOutBigCustomerChangeDO.class,
                            "where task_id=? and strategy_type=?",
                            taskDO.getId(), inputArgsDO.getStrategyType());

            if (!ListUtils.isEmpty(bigCustomerForecastDOList)) {
                List<BigCustomerChangeDTO> bigCustomerForecastList =
                        ListUtils.transform(bigCustomerForecastDOList, BigCustomerChangeDTO::fromDO);
                inputArgsDTO.setBigCustomerForecast(bigCustomerForecastList);
            }

            inputArgsDTOList.add(inputArgsDTO);
        }

        resp.setInputArgs(inputArgsDTOList);
        return resp;
    }

}
