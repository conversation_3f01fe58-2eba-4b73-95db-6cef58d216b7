package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.service.CosCreatePredictTaskService;
import cloud.demand.lab.modules.longterm.cos.web.req.QueryLastInputArgsReq;
import cloud.demand.lab.modules.longterm.cos.web.resp.QueryLastInputArgsResp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class CosCreatePredictTaskServiceImplTest {

    @Resource
    private CosCreatePredictTaskService cosCreatePredictTaskService;

    @Test
    public void testQueryLastInputArgs() {
        // 测试空参数
        QueryLastInputArgsReq req = new QueryLastInputArgsReq();
        QueryLastInputArgsResp resp = cosCreatePredictTaskService.queryLastInputArgs(req);
        assert resp != null;
        assert resp.getInputArgs() != null;
        
        // 测试不存在的categoryId
        req.setCategoryId(999999L);
        resp = cosCreatePredictTaskService.queryLastInputArgs(req);
        assert resp != null;
        assert resp.getInputArgs() != null;
        assert resp.getInputArgs().isEmpty();
        
        System.out.println("queryLastInputArgs test passed!");
    }
}
